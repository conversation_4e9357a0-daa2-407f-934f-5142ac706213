import backtrader as bt
import pandas as pd
import math
from xtquant import xtdata
from datetime import datetime

def get_hq(code):
    period = "1d"
    xtdata.enable_hello = False
    xtdata.download_history_data(stock_code=code, period=period, incrementally=True)
    history_data = xtdata.get_market_data_ex([], [code], period=period, count=-1, dividend_type="front_ratio")
    df = history_data[code]
    df['openinterest'] = 0
    df.index = pd.to_datetime(df.index.astype(str), format='%Y%m%d')
    return df

class MyStrategy(bt.Strategy):
    params = dict(
        grid_pct_down=0.05,
        grid_pct_up=0.08,
        max_open=5,#最大开仓次数，每次开仓五分之一
        min_unit=100
    )

    def __init__(self):
        self.order_list = []
        self.grid_positions = []  # 记录每一层网格的买入价和数量
        self.last_buy_price = None #记录最近的一次买入价格
        self.trade_log = []

    def log(self, txt):
        dt = self.datas[0].datetime.date(0)
        print(f'{dt.isoformat()} - {txt}')

    def next(self):
        data = self.datas[0]
        pos = self.getposition(data).size
        # print(data.datetime.date(0) ,pos)

        # 底分型识别逻辑（通俗处理：中间K线最低点比前一日和后一日都低）
       
        low_past20 = [data.low[i] for i in range(-20, 0)]
        is_bottom = (
            data.low[-1] < data.low[0] and data.low[-1] < data.low[-2] and
            data.low[-1] == min(low_past20)
        )

        # 当前总市值
        # total_value = self.broker.getvalue()
        cash = self.broker.get_cash()
        cur_price = data.close[0]

        if pos == 0 and is_bottom:
            # 初次开仓
            size = math.floor(cash /self.p.max_open/ cur_price / self.p.min_unit) * self.p.min_unit
            if size > 0:
                self.last_buy_price = cur_price
                self.grid_positions.append((cur_price, size))
                order = self.buy(size=size)
                self.order_list.append(order)
                self.log_trade(data._name, "BUY", cur_price, size)

        elif pos > 0:
            # 止盈卖出 先卖出现有止盈的持仓
            new_grid_positions = []
            for buy_price, size in self.grid_positions:
                if cur_price >= buy_price * (1 + self.p.grid_pct_up):
                    order = self.sell(size=size)
                    self.order_list.append(order)
                    self.log_trade(data._name, "SELL", cur_price, size)
                else:
                    new_grid_positions.append((buy_price, size))
            self.grid_positions = new_grid_positions
            
            # 网格加仓
            if self.grid_positions:
                # last_price, last_size = self.grid_positions[-1]
                
                # 补仓
                if cur_price < self.last_buy_price * (1 - self.p.grid_pct_down):
                    
                    left_open = self.p.max_open - len(self.grid_positions) #剩余开仓次数
                    if left_open>0:
                        size = math.floor(cash / left_open /cur_price / self.p.min_unit) * self.p.min_unit
                        if size > 0:
                            self.last_buy_price = cur_price
                            self.grid_positions.append((cur_price, size))
                            order = self.buy(size=size)
                            self.order_list.append(order)
                            self.log_trade(data._name, "BUY", cur_price, size)

            

    def notify_order(self, order):
        if order.status in [order.Completed]:
            self.order_list.remove(order)

    def log_trade(self, symbol, side, price, size):
        trade = {
            'symbol': symbol,
            'datetime': self.datas[0].datetime.datetime(0),
            'side': side,
            'price': price,
            'size': size
        }
        self.trade_log.append(trade)

    def stop(self):
        df = pd.DataFrame(self.trade_log)
        df.to_csv('trade_log.csv', index=False)
        self.log("交易记录已保存至 trade_log.csv")

if __name__ == "__main__":
    code = 'xxxxxx.SZ'
    df = get_hq(code)
    cerebro = bt.Cerebro()
    data = bt.feeds.PandasData(dataname=df, name=code ,fromdate=datetime(2024, 1, 1))
    cerebro.adddata(data)
    cerebro.addstrategy(MyStrategy)
    cerebro.broker.setcash(100000)
    cerebro.addanalyzer(bt.analyzers.Transactions, _name='txn')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TimeReturn, _name='timereturn')
    
    
    result = cerebro.run()
    
    strategy = result[0]
    
    # 获取分析结果
    returns = strategy.analyzers.returns.get_analysis()
    sharpe = strategy.analyzers.sharpe.get_analysis()
    drawdown = strategy.analyzers.drawdown.get_analysis()
    txn = strategy.analyzers.txn.get_analysis()

    print(f"总收益率: {returns['rtot']:.2%}")
    print(f"年化收益率: {returns['rnorm']:.2%}")
    print(f"日均收益率: {returns['ravg']:.4%}")
    
    print(f"当前回撤: {drawdown['drawdown']:.2f}%")
    print(f"当前亏损金额: {drawdown['moneydown']:.2f}")
    print(f"当前回撤持续时间: {drawdown['len']} 天")

    print(f"最大历史回撤: {drawdown['max']['drawdown']:.2f}%")
    print(f"最大历史亏损金额: {drawdown['max']['moneydown']:.2f}")
    print(f"最大回撤持续时间: {drawdown['max']['len']} 天")
    
    print(f"夏普比率: {sharpe['sharperatio']:.2f}")

    print(f"交易记录为：{txn}")
    
    returns = strategy.analyzers.timereturn.get_analysis()
    returns_series = pd.Series(returns)
    cum_returns = (1 + returns_series).cumprod()

    import matplotlib.pyplot as plt
    plt.rcParams['font.family'] = 'SimHei'      # 黑体
    plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
    cum_returns.plot(title='资金曲线（净值）')
    cerebro.plot()